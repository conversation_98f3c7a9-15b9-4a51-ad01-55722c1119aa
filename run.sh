#!/bin/bash

# JerryVision Unified Application Runner
echo "🚀 Starting JerryVision Unified Application..."

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source .venv/bin/activate

# Install dependencies
echo "📥 Installing dependencies..."

if [[ "$(uname -m)" == "arm64" ]]; then
  echo "This Mac is running on Apple Silicon (arm64)."
  pip install -r requirements-arm.txt
else
  echo "This Mac is running on Intel (x86_64)."
  pip install -r requirements.txt
fi


# Start the application with correct Python path
echo "🎯 Starting Streamlit application..."
PYTHONPATH=. streamlit run app.py --server.port 8501 --server.address 0.0.0.0 