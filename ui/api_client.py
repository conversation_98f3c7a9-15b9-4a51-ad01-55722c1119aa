import streamlit as st
import pandas as pd
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Tu<PERSON>, Optional
from urllib.parse import urlparse, parse_qs

# Import data processor from core directory
from core.data_processor import DataProcessor

# Configure logging
logger = logging.getLogger(__name__)

# Lazy initialization of data processor to avoid circular imports
_data_processor = None

def clear_data_cache():
    """Clear the data cache to force reload"""
    # Clear session state cache only
    cache_keys = [
        'data_loaded', 'raw_data', 'resampled_data',
        'bucket_versions', 'bucket_resampled_datasets', 'data_summary',
        'executed_trades',
        'cache_status', 'default_time_range', 'cache_version', 'bucket_specs', 'data_loading'
    ]
    
    for key in cache_keys:
        if key in st.session_state:
            del st.session_state[key]

def get_data_processor():
    """Get or create data processor instance (lazy loading)"""
    global _data_processor
    if _data_processor is None:
        _data_processor = DataProcessor()
    return _data_processor

def parse_endpoint_params(endpoint: str) -> Dict[str, str]:
    """Parse query parameters from endpoint URL"""
    if '?' not in endpoint:
        return {}

    # Split endpoint and query string
    _, query_string = endpoint.split('?', 1)

    # Parse query parameters
    params = {}
    for param in query_string.split('&'):
        if '=' in param:
            key, value = param.split('=', 1)
            params[key] = value

    return params

def call_api(endpoint, method="GET", data=None):
    """Simulate API calls by directly calling data processor methods"""
    try:
        data_processor = get_data_processor()
        
        if endpoint == "/data/refresh" and method == "POST":
            result = data_processor.refresh_data()
            return result
        elif endpoint == "/data/status":
            return data_processor.get_cache_status()
        elif endpoint == "/data/summary":
            raw_df, resampled_df, bucket_version_df, bucket_spec_df, executed_trades_df, trade_reports_df, backtest_latest_saved_df = data_processor.load_from_cache()
            
            summary = {
                "raw_data": {
                    "exists": not raw_df.empty,
                    "rows": len(raw_df),
                    "date_range": None
                },
                "resampled_data": {
                    "exists": not resampled_df.empty,
                    "rows": len(resampled_df),
                    "date_range": None
                },
                "bucket_version": {
                    "exists": not bucket_version_df.empty,
                    "rows": len(bucket_version_df),
                    "date_range": None
                },
                "bucket_spec": {
                    "exists": not bucket_spec_df.empty,
                    "rows": len(bucket_spec_df),
                    "date_range": None
                },

                "executed_trades": {
                    "exists": not executed_trades_df.empty,
                    "rows": len(executed_trades_df),
                    "date_range": None
                },
                "bucket_resampled_datasets": {
                    "exists": False,
                    "count": 0,
                    "datasets": []
                }
            }
            
            if not raw_df.empty:
                try:
                    summary["raw_data"]["date_range"] = {
                        "start": raw_df.index.min().strftime("%Y-%m-%d"),
                        "end": raw_df.index.max().strftime("%Y-%m-%d")
                    }
                except:
                    pass
                    
            if not resampled_df.empty:
                try:
                    summary["resampled_data"]["date_range"] = {
                        "start": resampled_df.index.min().strftime("%Y-%m-%d"),
                        "end": resampled_df.index.max().strftime("%Y-%m-%d")
                    }
                except:
                    pass
            
            return summary
        elif endpoint.startswith("/data/raw"):
            # Parse parameters from endpoint
            params = parse_endpoint_params(endpoint)
            limit = int(params.get('limit', 0)) if params.get('limit') else None
            start_date = params.get('start_date')
            end_date = params.get('end_date')
            columns = params.get('columns')

            # Convert columns string to list if provided
            if columns:
                columns = [col.strip() for col in columns.split(',')]

            # Load data with filters
            raw_df = data_processor.load_raw_data_from_cache(
                start_date=start_date,
                end_date=end_date,
                columns=columns
            )

            if raw_df.empty:
                return None

            # Apply limit after filtering
            if limit and len(raw_df) > limit:
                raw_df = raw_df.tail(limit)

            raw_df = raw_df.fillna(0).infer_objects(copy=False)

            return {
                "rows": len(raw_df),
                "columns": list(raw_df.columns),
                "data": raw_df.reset_index().to_dict('records')
            }
        elif endpoint.startswith("/data/resampled"):
            # Parse parameters from endpoint
            params = parse_endpoint_params(endpoint)
            limit = int(params.get('limit', 1000)) if params.get('limit') else 1000
            start_date = params.get('start_date')
            end_date = params.get('end_date')
            columns = params.get('columns')

            # Convert columns string to list if provided
            if columns:
                columns = [col.strip() for col in columns.split(',')]

            # Load data with filters
            resampled_df = data_processor.load_resampled_data_from_cache(
                start_date=start_date,
                end_date=end_date,
                columns=columns
            )

            if resampled_df.empty:
                return None

            # Apply limit after filtering
            if limit and len(resampled_df) > limit:
                resampled_df = resampled_df.tail(limit)

            resampled_df = resampled_df.fillna(0).infer_objects(copy=False)

            # Reset index and rename the date column to timestamp for consistency
            result_df = resampled_df.reset_index()
            if 'date' in result_df.columns:
                result_df = result_df.rename(columns={'date': 'timestamp'})

            return {
                "rows": len(result_df),
                "columns": list(result_df.columns),
                "data": result_df.to_dict('records')
            }
        elif endpoint.startswith("/data/ndx-resampled"):
            # Parse parameters from endpoint
            params = parse_endpoint_params(endpoint)
            limit = int(params.get('limit', 1000)) if params.get('limit') else 1000
            start_date = params.get('start_date')
            end_date = params.get('end_date')
            columns = params.get('columns')

            # Convert columns string to list if provided
            if columns:
                columns = [col.strip() for col in columns.split(',')]

            # Load NDX resampled data with filters
            ndx_resampled_df = data_processor.load_ndx_resampled_data_from_cache(
                start_date=start_date,
                end_date=end_date,
                columns=columns
            )

            if ndx_resampled_df.empty:
                return None

            # Apply limit after filtering
            if limit and len(ndx_resampled_df) > limit:
                ndx_resampled_df = ndx_resampled_df.tail(limit)

            ndx_resampled_df = ndx_resampled_df.fillna(0).infer_objects(copy=False)

            # Reset index and rename the date column to timestamp for consistency
            result_df = ndx_resampled_df.reset_index()
            if 'date' in result_df.columns:
                result_df = result_df.rename(columns={'date': 'timestamp'})

            return {
                "rows": len(result_df),
                "columns": list(result_df.columns),
                "data": result_df.to_dict('records')
            }

        elif endpoint.startswith("/data/ndx"):
            # Parse parameters from endpoint
            params = parse_endpoint_params(endpoint)
            limit = int(params.get('limit', 1000)) if params.get('limit') else 1000
            start_date = params.get('start_date')
            end_date = params.get('end_date')
            columns = params.get('columns')

            # Convert columns string to list if provided
            if columns:
                columns = [col.strip() for col in columns.split(',')]

            # Load NDX data with filters
            ndx_df = data_processor.load_ndx_data_from_cache(
                start_date=start_date,
                end_date=end_date,
                columns=columns
            )

            if ndx_df.empty:
                return None

            # Apply limit after filtering
            if limit and len(ndx_df) > limit:
                ndx_df = ndx_df.tail(limit)

            ndx_df = ndx_df.fillna(0).infer_objects(copy=False)

            return {
                "rows": len(ndx_df),
                "columns": list(ndx_df.columns),
                "data": ndx_df.reset_index().to_dict('records')
            }
        elif endpoint.startswith("/data/vix"):
            # Parse parameters from endpoint
            params = parse_endpoint_params(endpoint)
            limit = int(params.get('limit', 1000)) if params.get('limit') else 1000
            start_date = params.get('start_date')
            end_date = params.get('end_date')
            columns = params.get('columns')

            # Convert columns string to list if provided
            if columns:
                columns = [col.strip() for col in columns.split(',')]

            # Load VIX data with filters
            vix_df = data_processor.load_vix_data_from_cache(
                start_date=start_date,
                end_date=end_date,
                columns=columns
            )

            if vix_df.empty:
                return None

            # Apply limit after filtering
            if limit and len(vix_df) > limit:
                vix_df = vix_df.tail(limit)

            vix_df = vix_df.fillna(0).infer_objects(copy=False)

            return {
                "rows": len(vix_df),
                "columns": list(vix_df.columns),
                "data": vix_df.reset_index().to_dict('records')
            }
        elif endpoint == "/data/default-time-range":
            from core.config import settings
            return {
                "start_time": settings.DEFAULT_START_TIME,
                "end_time": settings.DEFAULT_END_TIME
            }
        elif endpoint.startswith("/data/available-dates"):
            # Parse parameters from endpoint
            params = parse_endpoint_params(endpoint)
            bucket_name = params.get('bucket_name')

            if bucket_name:
                # Get available dates for specific bucket
                bucket_resampled_datasets = data_processor.load_bucket_resampled_from_cache(bucket_name=bucket_name)
                if bucket_name in bucket_resampled_datasets:
                    df = bucket_resampled_datasets[bucket_name]
                    if not df.empty and 'timestamp' in df.columns:
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        available_dates = sorted(pd.Series(df['timestamp'].dt.date).unique())
                        return {
                            "dates": [date.strftime('%Y-%m-%d') for date in available_dates]
                        }

            return {"dates": []}
        elif endpoint == "/data/bucket-versions":
            _, _, bucket_version_df, _, _, _, _ = data_processor.load_from_cache()
            if bucket_version_df.empty:
                return {"data": []}
            
            return {
                "data": bucket_version_df.reset_index().to_dict('records')
            }
        elif endpoint.startswith("/data/bucket-versions/") and method == "GET":
            bucket_version_id = int(endpoint.split("/")[-1])
            _, _, bucket_version_df, _, _, _, _ = data_processor.load_from_cache()
            if bucket_version_df.empty:
                return None
            
            bucket_version = bucket_version_df[bucket_version_df['id'] == bucket_version_id]
            if bucket_version.empty:
                return None
            
            return bucket_version.iloc[0].to_dict()
        elif endpoint == "/data/bucket-versions" and method == "POST":
            # Create bucket version
            try:
                if not data or 'version' not in data:
                    return {"success": False, "message": "Version name is required"}
                
                db = get_data_processor()._get_db()
                success = db.create_bucket_version(data['version'])
                
                if success:
                    # Clear cache to force reload
                    clear_data_cache()
                    return {"success": True, "message": "Bucket version created successfully"}
                else:
                    return {"success": False, "message": "Failed to create bucket version"}
            except Exception as e:
                return {"success": False, "message": f"Error creating bucket version: {str(e)}"}
        elif endpoint.startswith("/data/bucket-versions/") and method == "PUT":
            bucket_version_id = int(endpoint.split("/")[-1])
            # Update bucket version
            try:
                if not data or 'version' not in data:
                    return {"success": False, "message": "Version name is required"}
                
                db = get_data_processor()._get_db()
                success = db.update_bucket_version(bucket_version_id, data['version'])
                
                if success:
                    # Clear cache to force reload
                    clear_data_cache()
                    return {"success": True, "message": "Bucket version updated successfully"}
                else:
                    return {"success": False, "message": "Failed to update bucket version"}
            except Exception as e:
                return {"success": False, "message": f"Error updating bucket version: {str(e)}"}
        elif endpoint.startswith("/data/bucket-versions/") and method == "DELETE":
            bucket_version_id = int(endpoint.split("/")[-1])
            # Delete bucket version
            try:
                db = get_data_processor()._get_db()
                success = db.delete_bucket_version(bucket_version_id)
                
                if success:
                    # Clear cache to force reload
                    clear_data_cache()
                    return {"success": True, "message": "Bucket version deleted successfully"}
                else:
                    return {"success": False, "message": "Failed to delete bucket version"}
            except Exception as e:
                return {"success": False, "message": f"Error deleting bucket version: {str(e)}"}
        elif endpoint.startswith("/data/bucket-specs"):
            _, _, _, bucket_spec_df, _, _, _ = data_processor.load_from_cache()
            if bucket_spec_df.empty:
                return {"data": []}
            
            # Parse bucket_version_id parameter if present
            if "bucket_version_id=" in endpoint:
                bucket_version_id = int(endpoint.split("bucket_version_id=")[1])
                bucket_spec_df = bucket_spec_df[bucket_spec_df['bucket_version_id'] == bucket_version_id]
            
            return {
                "data": bucket_spec_df.reset_index().to_dict('records')
            }

        elif endpoint == "/data/executed-trades":
            _, _, _, _, executed_trades_df, _, _ = data_processor.load_from_cache()
            if executed_trades_df.empty:
                return {"data": []}

            return {
                "data": executed_trades_df.reset_index().to_dict('records')
            }
        elif endpoint.startswith("/data/executed-trades/"):
            # Parse date parameter from endpoint like /data/executed-trades/2024-01-15
            date_str = endpoint.split("/")[-1]
            _, _, _, _, executed_trades_df, _, _ = data_processor.load_from_cache()

            if executed_trades_df.empty:
                return {"data": []}

            # Filter by date using entry_time (primary) or fallback to other date columns
            if 'entry_time' in executed_trades_df.columns:
                # Create a copy to avoid modifying the original dataframe
                df_copy = executed_trades_df.copy()
                # Extract date part from entry_time and convert to string for comparison
                df_copy['trade_date'] = pd.to_datetime(df_copy['entry_time']).dt.strftime('%Y-%m-%d')
                filtered_df = df_copy[df_copy['trade_date'] == date_str]
                # Remove the temporary trade_date column
                if 'trade_date' in filtered_df.columns:
                    filtered_df = filtered_df.drop(columns=['trade_date'])
            elif 'date' in executed_trades_df.columns:
                # Fallback to date column if available
                df_copy = executed_trades_df.copy()
                df_copy['trade_date'] = pd.to_datetime(df_copy['date']).dt.strftime('%Y-%m-%d')
                filtered_df = df_copy[df_copy['trade_date'] == date_str]
                if 'trade_date' in filtered_df.columns:
                    filtered_df = filtered_df.drop(columns=['trade_date'])
            elif 'trading_date' in executed_trades_df.columns:
                # Fallback to trading_date column if available
                df_copy = executed_trades_df.copy()
                df_copy['trade_date'] = pd.to_datetime(df_copy['trading_date']).dt.strftime('%Y-%m-%d')
                filtered_df = df_copy[df_copy['trade_date'] == date_str]
                if 'trade_date' in filtered_df.columns:
                    filtered_df = filtered_df.drop(columns=['trade_date'])
            else:
                # No suitable date column found, return all data
                filtered_df = executed_trades_df

            return {
                "data": filtered_df.reset_index().to_dict('records')
            }

        elif endpoint == "/data/trade-reports":
            _, _, _, _, _, trade_reports_df, _ = data_processor.load_from_cache()
            if trade_reports_df.empty:
                return {"data": []}

            return {
                "data": trade_reports_df.reset_index().to_dict('records')
            }
        elif endpoint.startswith("/data/trade-reports/"):
            # Parse date parameter from endpoint like /data/trade-reports/2024-01-15
            date_str = endpoint.split("/")[-1]
            _, _, _, _, _, trade_reports_df, _ = data_processor.load_from_cache()

            if trade_reports_df.empty:
                return {"data": []}

            # Filter by date using similar logic as executed trades
            if 'date' in trade_reports_df.columns:
                # Create a copy to avoid modifying the original dataframe
                df_copy = trade_reports_df.copy()
                df_copy['report_date'] = pd.to_datetime(df_copy['date']).dt.strftime('%Y-%m-%d')
                filtered_df = df_copy[df_copy['report_date'] == date_str]
                # Remove the temporary report_date column
                if 'report_date' in filtered_df.columns:
                    filtered_df = filtered_df.drop(columns=['report_date'])
            elif 'created_at' in trade_reports_df.columns:
                # Fallback to created_at column if available
                df_copy = trade_reports_df.copy()
                df_copy['report_date'] = pd.to_datetime(df_copy['created_at']).dt.strftime('%Y-%m-%d')
                filtered_df = df_copy[df_copy['report_date'] == date_str]
                if 'report_date' in filtered_df.columns:
                    filtered_df = filtered_df.drop(columns=['report_date'])
            else:
                # No suitable date column found, return all data
                filtered_df = trade_reports_df

            return {
                "data": filtered_df.reset_index().to_dict('records')
            }

        elif endpoint.startswith("/data/bucket-specs/") and method == "GET":
            bucket_spec_id = int(endpoint.split("/")[-1])
            _, _, _, bucket_spec_df, _, _, _ = data_processor.load_from_cache()
            if bucket_spec_df.empty:
                return None
            
            bucket_spec = bucket_spec_df[bucket_spec_df['id'] == bucket_spec_id]
            if bucket_spec.empty:
                return None
            
            return bucket_spec.iloc[0].to_dict()
        elif endpoint == "/data/bucket-specs" and method == "POST":
            # Create bucket spec
            try:
                if not data or 'bucket_start' not in data or 'bucket_version_id' not in data:
                    return {"success": False, "message": "Bucket start time and version ID are required"}
                
                logger.info(f"Creating bucket spec with data: {data}")
                
                db = get_data_processor()._get_db()
                success = db.create_bucket_spec(data['bucket_start'], data['bucket_version_id'])
                
                if success:
                    # Clear cache to force reload
                    clear_data_cache()
                    return {"success": True, "message": "Bucket spec created successfully"}
                else:
                    return {"success": False, "message": "Failed to create bucket spec - check database logs for details"}
            except Exception as e:
                logger.error(f"Exception in bucket spec creation: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return {"success": False, "message": f"Error creating bucket spec: {str(e)}"}
        elif endpoint.startswith("/data/bucket-specs/") and method == "PUT":
            bucket_spec_id = int(endpoint.split("/")[-1])
            # Update bucket spec
            try:
                if not data or 'bucket_start' not in data:
                    return {"success": False, "message": "Bucket start time is required"}
                
                db = get_data_processor()._get_db()
                success = db.update_bucket_spec(bucket_spec_id, data['bucket_start'])
                
                if success:
                    # Clear cache to force reload
                    clear_data_cache()
                    return {"success": True, "message": "Bucket spec updated successfully"}
                else:
                    return {"success": False, "message": "Failed to update bucket spec"}
            except Exception as e:
                return {"success": False, "message": f"Error updating bucket spec: {str(e)}"}
        elif endpoint.startswith("/data/bucket-specs/") and method == "DELETE":
            bucket_spec_id = int(endpoint.split("/")[-1])
            # Delete bucket spec
            try:
                db = get_data_processor()._get_db()
                success = db.delete_bucket_spec(bucket_spec_id)
                
                if success:
                    # Clear cache to force reload
                    clear_data_cache()
                    return {"success": True, "message": "Bucket spec deleted successfully"}
                else:
                    return {"success": False, "message": "Failed to delete bucket spec"}
            except Exception as e:
                return {"success": False, "message": f"Error deleting bucket spec: {str(e)}"}
        elif endpoint == "/data/bucket-resampled":
            bucket_resampled_datasets = data_processor.load_bucket_resampled_from_cache()
            datasets = []
            for bucket_name, df in bucket_resampled_datasets.items():
                datasets.append({
                    "bucket_name": bucket_name,
                    "rows": len(df),
                    "date_range": {
                        "start": df.index.min().strftime("%Y-%m-%d") if not df.empty else None,
                        "end": df.index.max().strftime("%Y-%m-%d") if not df.empty else None
                    }
                })
            
            return {
                "datasets": datasets,
                "count": len(datasets)
            }
        elif endpoint.startswith("/data/bucket-resampled/"):
            bucket_name = endpoint.split("/")[-1].split("?")[0]

            # Parse parameters from endpoint
            params = parse_endpoint_params(endpoint)
            limit = int(params.get('limit', 1000)) if params.get('limit') else 1000
            start_date = params.get('start_date')
            end_date = params.get('end_date')
            columns = params.get('columns')

            # Convert columns string to list if provided
            if columns:
                columns = [col.strip() for col in columns.split(',')]

            # Load bucket resampled data with filters
            bucket_resampled_datasets = data_processor.load_bucket_resampled_from_cache(
                bucket_name=bucket_name,
                start_date=start_date,
                end_date=end_date,
                columns=columns
            )

            if bucket_name not in bucket_resampled_datasets:
                return None

            df = bucket_resampled_datasets[bucket_name]

            # Apply limit after filtering
            if limit and len(df) > limit:
                df = df.tail(limit)

            df = df.fillna(0).infer_objects(copy=False)

            return {
                "rows": len(df),
                "columns": list(df.columns),
                "data": df.reset_index().to_dict('records')
            }
        elif endpoint == "/api/v1/config":
            from core.config import settings
            return {
                "api_base_url": settings.API_BASE_URL
            }
        elif endpoint == "/data/backtest-latest-saved":
            # Load backtest_latest_saved data from cache
            _, _, _, _, _, _, backtest_latest_saved_df = data_processor.load_from_cache()
            if backtest_latest_saved_df.empty:
                return {"data": []}
            return {
                "rows": len(backtest_latest_saved_df),
                "columns": list(backtest_latest_saved_df.columns),
                "data": backtest_latest_saved_df.reset_index().to_dict('records')
            }
        else:
            st.error(f"Unsupported endpoint: {endpoint}")
            return None
    except Exception as e:
        st.error(f"Error calling API: {str(e)}")
        import traceback
        st.error(f"Traceback: {traceback.format_exc()}")
        return None

def refresh_data():
    """Refresh data from database - simple synchronous approach"""
    try:
        with st.spinner("🔄 Refreshing data from database..."):
            result = call_api("/data/refresh", method="POST")
            if result and result.get("success"):
                st.success(f"✅ Data refreshed successfully! {result['raw_rows']} 10s rows, {result['resampled_rows']} 30s rows")
                # Clear session state cache to force reload
                clear_data_cache()
                return True
            else:
                st.error(f"❌ Failed to refresh data: {result.get('message', 'Unknown error')}")
                return False
    except Exception as e:
        st.error(f"❌ Error during refresh: {str(e)}")
        return False

# API functions (no caching - using manual session state caching instead)
def get_data_summary():
    return call_api("/data/summary")

def get_cache_status():
    return call_api("/data/status")

def get_raw_data(limit=1000):
    return call_api(f"/data/raw?limit={limit}")

def get_resampled_data(limit=1000):
    return call_api(f"/data/resampled?limit={limit}")

def get_default_time_range():
    return call_api("/data/default-time-range")

def get_bucket_versions():
    return call_api("/data/bucket-versions")

def get_bucket_version(bucket_version_id):
    return call_api(f"/data/bucket-versions/{bucket_version_id}")

def create_bucket_version(bucket_version_data):
    """Create a new bucket version"""
    return call_api("/data/bucket-versions", method="POST", data=bucket_version_data)

def update_bucket_version(bucket_version_id, bucket_version_data):
    """Update an existing bucket version"""
    return call_api(f"/data/bucket-versions/{bucket_version_id}", method="PUT", data=bucket_version_data)

def delete_bucket_version(bucket_version_id):
    """Delete a bucket version"""
    return call_api(f"/data/bucket-versions/{bucket_version_id}", method="DELETE")

def get_bucket_specs(bucket_version_id=None):
    endpoint = "/data/bucket-specs"
    if bucket_version_id:
        endpoint += f"?bucket_version_id={bucket_version_id}"
    return call_api(endpoint)

def get_bucket_spec(bucket_spec_id):
    """Get a specific bucket spec"""
    return call_api(f"/data/bucket-specs/{bucket_spec_id}")

def create_bucket_spec(bucket_spec_data):
    """Create a new bucket spec"""
    return call_api("/data/bucket-specs", method="POST", data=bucket_spec_data)

def update_bucket_spec(bucket_spec_id, bucket_spec_data):
    """Update an existing bucket spec"""
    return call_api(f"/data/bucket-specs/{bucket_spec_id}", method="PUT", data=bucket_spec_data)

def delete_bucket_spec(bucket_spec_id):
    """Delete a bucket spec"""
    return call_api(f"/data/bucket-specs/{bucket_spec_id}", method="DELETE")

def get_bucket_resampled_datasets():
    return call_api("/data/bucket-resampled")

def get_bucket_resampled_data(bucket_name, limit=1000):
    return call_api(f"/data/bucket-resampled/{bucket_name}?limit={limit}")

def get_executed_trades():
    """Get all executed trades data"""
    return call_api("/data/executed-trades")

def get_executed_trades_by_date(date):
    """Get executed trades data for a specific date"""
    return call_api(f"/data/executed-trades/{date}")

def get_trade_reports():
    """Get all trade reports data"""
    return call_api("/data/trade-reports")

def get_trade_reports_by_date(date):
    """Get trade reports data for a specific date"""
    return call_api(f"/data/trade-reports/{date}")